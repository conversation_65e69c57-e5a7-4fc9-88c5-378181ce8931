{"root": ["../../src/App.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/GameBoard/GameBoard.tsx", "../../src/components/GameBoard/index.ts", "../../src/components/GameBoard/__tests__/GameBoard.test.tsx", "../../src/components/GameCell/GameCell.tsx", "../../src/components/GameCell/index.ts", "../../src/components/GameCell/__tests__/GameCell.test.tsx", "../../src/components/GameControls/GameControls.tsx", "../../src/components/GameControls/index.ts", "../../src/components/GameStatus/GameStatus.tsx", "../../src/components/GameStatus/index.ts", "../../src/components/GameStatus/__tests__/GameStatus.test.tsx", "../../src/hooks/useGameState.ts", "../../src/services/gameService.ts", "../../src/test/integration.test.ts", "../../src/test/setup.ts", "../../src/types/game.ts", "../../src/utils/gameLogic.ts", "../../src/utils/__tests__/gameLogic.test.ts"], "errors": true, "version": "5.8.3"}