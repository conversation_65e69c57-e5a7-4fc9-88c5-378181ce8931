"use strict";var B=Object.defineProperty;var p=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var C=(i,e,E)=>e in i?B(i,e,{enumerable:!0,configurable:!0,writable:!0,value:E}):i[e]=E,A=(i,e)=>{for(var E in e||(e={}))b.call(e,E)&&C(i,E,e[E]);if(p)for(var E of p(e))g.call(e,E)&&C(i,E,e[E]);return i};var O,_,D,a,L,c,S,N,u,P;const r$2=Object.create(null),s=i=>{var e,E;return((e=globalThis.process)==null?void 0:e.env)||void 0||((E=globalThis.Deno)==null?void 0:E.env.toObject())||globalThis.__env__||(i?r$2:globalThis)},env=new Proxy(r$2,{get(i,e){var E;return(E=s()[e])!=null?E:r$2[e]},has(i,e){const E=s();return e in E||e in r$2},set(i,e,E){const n=s(!0);return n[e]=E,!0},deleteProperty(i,e){if(!e)return!1;const E=s(!0);return delete E[e],!0},ownKeys(){const i=s(!0);return Object.keys(i)}}),nodeENV=typeof process<"u"&&process.env&&process.env.NODE_ENV||"",r$1=[["APPVEYOR"],["AWS_AMPLIFY","AWS_APP_ID",{ci:!0}],["AZURE_PIPELINES","SYSTEM_TEAMFOUNDATIONCOLLECTIONURI"],["AZURE_STATIC","INPUT_AZURE_STATIC_WEB_APPS_API_TOKEN"],["APPCIRCLE","AC_APPCIRCLE"],["BAMBOO","bamboo_planKey"],["BITBUCKET","BITBUCKET_COMMIT"],["BITRISE","BITRISE_IO"],["BUDDY","BUDDY_WORKSPACE_ID"],["BUILDKITE"],["CIRCLE","CIRCLECI"],["CIRRUS","CIRRUS_CI"],["CLOUDFLARE_PAGES","CF_PAGES",{ci:!0}],["CLOUDFLARE_WORKERS","WORKERS_CI",{ci:!0}],["CODEBUILD","CODEBUILD_BUILD_ARN"],["CODEFRESH","CF_BUILD_ID"],["DRONE"],["DRONE","DRONE_BUILD_EVENT"],["DSARI"],["GITHUB_ACTIONS"],["GITLAB","GITLAB_CI"],["GITLAB","CI_MERGE_REQUEST_ID"],["GOCD","GO_PIPELINE_LABEL"],["LAYERCI"],["HUDSON","HUDSON_URL"],["JENKINS","JENKINS_URL"],["MAGNUM"],["NETLIFY"],["NETLIFY","NETLIFY_LOCAL",{ci:!1}],["NEVERCODE"],["RENDER"],["SAIL","SAILCI"],["SEMAPHORE"],["SCREWDRIVER"],["SHIPPABLE"],["SOLANO","TDDIUM"],["STRIDER"],["TEAMCITY","TEAMCITY_VERSION"],["TRAVIS"],["VERCEL","NOW_BUILDER"],["VERCEL","VERCEL",{ci:!1}],["VERCEL","VERCEL_ENV",{ci:!1}],["APPCENTER","APPCENTER_BUILD_ID"],["CODESANDBOX","CODESANDBOX_SSE",{ci:!1}],["CODESANDBOX","CODESANDBOX_HOST",{ci:!1}],["STACKBLITZ"],["STORMKIT"],["CLEAVR"],["ZEABUR"],["CODESPHERE","CODESPHERE_APP_ID",{ci:!0}],["RAILWAY","RAILWAY_PROJECT_ID"],["RAILWAY","RAILWAY_SERVICE_ID"],["DENO-DEPLOY","DENO_DEPLOYMENT_ID"],["FIREBASE_APP_HOSTING","FIREBASE_APP_HOSTING",{ci:!0}]];function I(){var i,e,E,n,T,R;if((i=globalThis.process)!=null&&i.env)for(const l of r$1){const d=l[1]||l[0];if((e=globalThis.process)!=null&&e.env[d])return A({name:l[0].toLowerCase()},l[2])}return((n=(E=globalThis.process)==null?void 0:E.env)==null?void 0:n.SHELL)==="/bin/jsh"&&((R=(T=globalThis.process)==null?void 0:T.versions)!=null&&R.webcontainer)?{name:"stackblitz",ci:!1}:{name:"",ci:!1}}const providerInfo=I(),provider=providerInfo.name;function toBoolean(i){return i?i!=="false":!1}const platform=((O=globalThis.process)==null?void 0:O.platform)||"",isCI=toBoolean(env.CI)||providerInfo.ci!==!1,hasTTY=toBoolean(((_=globalThis.process)==null?void 0:_.stdout)&&((D=globalThis.process)==null?void 0:D.stdout.isTTY)),hasWindow=typeof window<"u",isDebug=toBoolean(env.DEBUG),isTest=nodeENV==="test"||toBoolean(env.TEST),isProduction=nodeENV==="production",isDevelopment=nodeENV==="dev"||nodeENV==="development",isMinimal=toBoolean(env.MINIMAL)||isCI||isTest||!hasTTY,isWindows=/^win/i.test(platform),isLinux=/^linux/i.test(platform),isMacOS=/^darwin/i.test(platform),isColorSupported=!toBoolean(env.NO_COLOR)&&(toBoolean(env.FORCE_COLOR)||(hasTTY||isWindows)&&env.TERM!=="dumb"||isCI),nodeVersion=(((L=(a=globalThis.process)==null?void 0:a.versions)==null?void 0:L.node)||"").replace(/^v/,"")||null,nodeMajorVersion=Number(nodeVersion==null?void 0:nodeVersion.split(".")[0])||null,o$1=globalThis.process||Object.create(null),r={versions:{}},process$1=new Proxy(o$1,{get(i,e){if(e==="env")return env;if(e in i)return i[e];if(e in r)return r[e]}}),isNode=((S=(c=globalThis.process)==null?void 0:c.release)==null?void 0:S.name)==="node",isBun=!!globalThis.Bun||!!((u=(N=globalThis.process)==null?void 0:N.versions)!=null&&u.bun),isDeno=!!globalThis.Deno,isFastly=!!globalThis.fastly,isNetlify=!!globalThis.Netlify,isEdgeLight=!!globalThis.EdgeRuntime,isWorkerd=((P=globalThis.navigator)==null?void 0:P.userAgent)==="Cloudflare-Workers",t=[[isNetlify,"netlify"],[isEdgeLight,"edge-light"],[isWorkerd,"workerd"],[isFastly,"fastly"],[isDeno,"deno"],[isBun,"bun"],[isNode,"node"]];function o(){const i=t.find(e=>e[0]);if(i)return{name:i[1]}}const runtimeInfo=o(),runtime=(runtimeInfo==null?void 0:runtimeInfo.name)||"";exports.env=env,exports.hasTTY=hasTTY,exports.hasWindow=hasWindow,exports.isBun=isBun,exports.isCI=isCI,exports.isColorSupported=isColorSupported,exports.isDebug=isDebug,exports.isDeno=isDeno,exports.isDevelopment=isDevelopment,exports.isEdgeLight=isEdgeLight,exports.isFastly=isFastly,exports.isLinux=isLinux,exports.isMacOS=isMacOS,exports.isMinimal=isMinimal,exports.isNetlify=isNetlify,exports.isNode=isNode,exports.isProduction=isProduction,exports.isTest=isTest,exports.isWindows=isWindows,exports.isWorkerd=isWorkerd,exports.nodeENV=nodeENV,exports.nodeMajorVersion=nodeMajorVersion,exports.nodeVersion=nodeVersion,exports.platform=platform,exports.process=process$1,exports.provider=provider,exports.providerInfo=providerInfo,exports.runtime=runtime,exports.runtimeInfo=runtimeInfo;
