/**
 * GameStatus component - displays current game status and player information
 */

import React from 'react';
import { type GameState } from '../../types/game';
import './GameStatus.css';

interface GameStatusProps {
  /** Current game state */
  gameState: GameState;
  /** Whether the game is loading */
  isLoading?: boolean;
}

export const GameStatus: React.FC<GameStatusProps> = ({
  gameState,
  isLoading = false,
}) => {
  const getStatusMessage = (): string => {
    if (isLoading) {
      return 'Loading...';
    }

    switch (gameState.status) {
      case 'playing':
        return `Player ${gameState.currentPlayer}'s turn`;
      case 'won':
        return `Player ${gameState.winner} wins!`;
      case 'draw':
        return "It's a draw!";
      default:
        return 'Game ready';
    }
  };

  const getStatusClass = (): string => {
    const baseClass = 'game-status__message';

    if (isLoading) {
      return `${baseClass} game-status__message--loading`;
    }

    switch (gameState.status) {
      case 'playing':
        return `${baseClass} game-status__message--playing`;
      case 'won':
        return `${baseClass} game-status__message--won`;
      case 'draw':
        return `${baseClass} game-status__message--draw`;
      default:
        return baseClass;
    }
  };

  return (
    <div className="game-status" data-testid="game-status">
      <div className={getStatusClass()}>
        {getStatusMessage()}
      </div>

      {gameState.moveCount > 0 && !isLoading && (
        <div className="game-status__info">
          <span className="game-status__moves">
            Moves: {gameState.moveCount}
          </span>
        </div>
      )}
    </div>
  );
};
