/**
 * Unit tests for GameStatus component
 */

import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { GameStatus } from '../';
import { GameState } from '../../../types/game';

describe('GameStatus', () => {
  const baseGameState: GameState = {
    board: Array(9).fill(null),
    currentPlayer: 'X',
    status: 'playing',
    winner: null,
    moveCount: 0,
    gameId: 'test-game',
  };

  it('should display current player turn', () => {
    render(<GameStatus gameState={baseGameState} />);

    expect(screen.getByText("Player X's turn")).toBeInTheDocument();
  });

  it('should display O player turn', () => {
    const gameState = { ...baseGameState, currentPlayer: 'O' as const };
    render(<GameStatus gameState={gameState} />);

    expect(screen.getByText("Player O's turn")).toBeInTheDocument();
  });

  it('should display winner message', () => {
    const gameState: GameState = {
      ...baseGameState,
      status: 'won',
      winner: 'X',
    };

    render(<GameStatus gameState={gameState} />);

    expect(screen.getByText('Player X wins!')).toBeInTheDocument();
  });

  it('should display draw message', () => {
    const gameState: GameState = {
      ...baseGameState,
      status: 'draw',
    };

    render(<GameStatus gameState={gameState} />);

    expect(screen.getByText("It's a draw!")).toBeInTheDocument();
  });

  it('should display loading message', () => {
    render(<GameStatus gameState={baseGameState} isLoading={true} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should display move count when moves have been made', () => {
    const gameState = { ...baseGameState, moveCount: 5 };
    render(<GameStatus gameState={gameState} />);

    expect(screen.getByText('Moves: 5')).toBeInTheDocument();
  });

  it('should not display move count when no moves made', () => {
    render(<GameStatus gameState={baseGameState} />);

    expect(screen.queryByText('Moves: 0')).not.toBeInTheDocument();
  });

  it('should not display move count when loading', () => {
    const gameState = { ...baseGameState, moveCount: 3 };
    render(<GameStatus gameState={gameState} isLoading={true} />);

    expect(screen.queryByText('Moves: 3')).not.toBeInTheDocument();
  });

  it('should have correct CSS classes for different states', () => {
    const { rerender } = render(<GameStatus gameState={baseGameState} />);

    // Playing state
    expect(screen.getByText("Player X's turn")).toHaveClass('game-status__message--playing');

    // Won state
    const wonState = { ...baseGameState, status: 'won' as const, winner: 'X' as const };
    rerender(<GameStatus gameState={wonState} />);
    expect(screen.getByText('Player X wins!')).toHaveClass('game-status__message--won');

    // Draw state
    const drawState = { ...baseGameState, status: 'draw' as const };
    rerender(<GameStatus gameState={drawState} />);
    expect(screen.getByText("It's a draw!")).toHaveClass('game-status__message--draw');

    // Loading state
    rerender(<GameStatus gameState={baseGameState} isLoading={true} />);
    expect(screen.getByText('Loading...')).toHaveClass('game-status__message--loading');
  });
});
