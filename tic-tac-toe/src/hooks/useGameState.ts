/**
 * TanStack Query hooks for game state management
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import * as gameService from "../services/gameService";
import { type GameState } from "../types/game";

// Query keys
export const QUERY_KEYS = {
  gameState: ["gameState"] as const,
  gameStats: ["gameStats"] as const,
};

/**
 * Hook to get current game state
 */
export const useGameState = () => {
  return useQuery({
    queryKey: QUERY_KEYS.gameState,
    queryFn: gameService.getGameState,
    staleTime: 0, // Always refetch to ensure consistency
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook to get game statistics
 */
export const useGameStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.gameStats,
    queryFn: gameService.getGameStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to make a move in the game
 */
export const useGameMove = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      gameState,
      position,
    }: {
      gameState: GameState;
      position: number;
    }) => gameService.makeMove(gameState, position),
    onSuccess: (newGameState) => {
      // Update the game state in cache
      queryClient.setQueryData(QUERY_KEYS.gameState, newGameState);

      // Invalidate stats to refetch updated statistics
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.gameStats });
    },
    onError: (error) => {
      console.error("Failed to make move:", error);
    },
  });
};

/**
 * Hook to reset the game
 */
export const useResetGame = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: gameService.resetGame,
    onSuccess: (newGameState) => {
      // Update the game state in cache
      queryClient.setQueryData(QUERY_KEYS.gameState, newGameState);
    },
    onError: (error) => {
      console.error("Failed to reset game:", error);
    },
  });
};

/**
 * Hook to clear all game data
 */
export const useClearGameData = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: gameService.clearGameData,
    onSuccess: () => {
      // Invalidate all queries to refetch fresh data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.gameState });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.gameStats });
    },
    onError: (error) => {
      console.error("Failed to clear game data:", error);
    },
  });
};
